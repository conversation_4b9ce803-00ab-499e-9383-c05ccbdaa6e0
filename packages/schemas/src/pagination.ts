import { z } from "zod";



export const pagePaginationSchema = z.object({
	page: z.number().int().min(1).default(1),
	limit: z.number().int().min(1).max(100).default(10),
	search: z
		.union([
			z.string(),
			z.object({
				term: z.string(),
				fields: z.array(z.string()).optional(),
				mode: z.enum(["insensitive", "default"]).default("insensitive"),
			}),
		])
		.optional(),
	sort: z
		.union([
			z.string(), // Simple string format
			z.object({
				field: z.string(),
				order: z.enum(["asc", "desc"]).default("asc"),
			}),
			z.array(
				z.object({
					field: z.string(),
					order: z.enum(["asc", "desc"]).default("asc"),
				}),
			),
		])
		.optional(),
	filter: z.record(z.unknown()).optional(),
	include: z.record(z.unknown()).optional(),
	select: z.record(z.unknown()).optional(),
	where: z.record(z.unknown()).optional(),
	orderBy: z.unknown().optional(), // Full Prisma orderBy
	
});

// Cursor-based pagination for infinite scroll
export const cursorPaginationSchema = z.object({
	cursor: z.string().optional(),
	limit: z.number().int().min(1).max(100).default(20),
	search: z
		.union([
			z.string(),
			z.object({
				term: z.string(),
				fields: z.array(z.string()).optional(),
				mode: z.enum(["insensitive", "default"]).default("insensitive"),
			}),
		])
		.optional(),
	sort: z
		.union([
			z.string(),
			z.object({
				field: z.string(),
				order: z.enum(["asc", "desc"]).default("asc"),
			}),
		])
		.optional(),
	filter: z.record(z.unknown()).optional(),
	include: z.record(z.unknown()).optional(),
	select: z.record(z.unknown()).optional(),
	where: z.record(z.unknown()).optional(),
	orderBy: z.unknown().optional(), // Full Prisma orderBy
	
});



// Standard pagination response format
export const pagePaginationResponseSchema = z.object({
	data: z.array(z.unknown()),
	meta: z.object({
		total: z.number().int().min(0),
		page: z.number().int().min(1).optional(), // Optional for cursor pagination
		limit: z.number().int().min(1),
		totalPages: z.number().int().min(0).optional(), // Optional for cursor pagination
		hasNext: z.boolean(),
		hasPrev: z.boolean(),
	}),
});

// Cursor pagination response
export const cursorPaginationResponseSchema = z.object({
	data: z.array(z.unknown()),
	meta: z.object({
		total: z.number().int().min(0).optional(), // May not be available for performance
		limit: z.number().int().min(1),
		hasNext: z.boolean(),
		hasPrev: z.boolean(),
		nextCursor: z.string().optional(),
		prevCursor: z.string().optional(),
	}),
});

// ===== TYPE DEFINITIONS =====

export type PagePaginationParams = z.infer<typeof pagePaginationSchema>;
export type CursorPaginationParams = z.infer<typeof cursorPaginationSchema>;

// Response types
export type PaginationResponse<T> = Omit<z.infer<typeof pagePaginationResponseSchema>, "data"> & {
	data: T[];
};

export type CursorPaginationResponse<T> = Omit<z.infer<typeof cursorPaginationResponseSchema>, "data"> & {
	data: T[];
};

// Alias for backward compatibility
export type SimplePaginationParams = PagePaginationParams;
export type StructuredPaginationParams = PagePaginationParams;

// ===== UTILITY FUNCTIONS =====

/**
 * Parse sort string format "field:order" into object
 */
export function parseSortString(sortStr: string): { field: string; order: "asc" | "desc" } {
	const [field, order = "asc"] = sortStr.split(":");
	if (!field || field.trim() === "") {
		throw new Error(`Invalid sort string: "${sortStr}". Expected format: "field:order"`);
	}
	return {
		field: field.trim(),
		order: (order.trim().toLowerCase() === "desc" ? "desc" : "asc") as "asc" | "desc"
	};
}

/**
 * Transform simple pagination params to prisma-helpers format
 */
export function transformSimpleToPrismaHelpers(params: SimplePaginationParams, searchFields: string[] = ["name", "email"]) {
	const { page = 1, limit = 10, search, sort, filter } = params;

	// Build search params if provided
	const searchParams = search ? (
		typeof search === "string"
			? { fields: searchFields, term: search, mode: "insensitive" as const }
			: {
				fields: search.fields || searchFields,
				term: search.term,
				mode: search.mode
			}
	) : undefined;

	// Build sort params if provided (parse string format "field:order")
	const orderBy = sort ? (
		typeof sort === "string"
			? (() => {
				const { field, order } = parseSortString(sort);
				return { [field]: order };
			})()
			: Array.isArray(sort)
				? sort.map(s => ({ [s.field]: s.order }))
				: { [sort.field]: sort.order }
	) : undefined;

	return {
		page,
		limit,
		search: searchParams,
		filters: filter,
		orderBy,
	};
}

/**
 * Transform structured pagination params to prisma-helpers format
 */
export function transformStructuredToPrismaHelpers(params: StructuredPaginationParams, defaultSearchFields: string[] = ["name", "email"]) {
	return transformSimpleToPrismaHelpers(params, defaultSearchFields);
}





