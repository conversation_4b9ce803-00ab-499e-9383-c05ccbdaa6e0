/**
 * Examples demonstrating the new generic pagination system
 * 
 * Shows how to use the pagination API with proper typing and no `any` types
 */

import { paginate } from "../lib/pagination";

// ===== SIMPLE LAYER EXAMPLES =====

/**
 * Example 1: Basic user pagination
 */
export async function basicUserPagination() {
	console.log("🔹 Simple Layer: Basic user pagination");
	
	const users = await paginate.users({
		page: 1,
		limit: 10,
	});
	
	console.log(`Found ${users.meta.total} users, showing page ${users.meta.page} of ${users.meta.totalPages}`);
	console.log(`Data type:`, typeof users.data[0]); // Properly typed
	return users;
}

/**
 * Example 2: User search with sorting
 */
export async function searchUsersWithSort() {
	console.log("🔹 Simple Layer: Search users with sorting");
	
	const users = await paginate.users({
		page: 1,
		limit: 20,
		search: "john",           // Simple string search
		sort: "createdAt:desc",   // Simple string sort
	});
	
	console.log(`Found ${users.data.length} users matching "john"`);
	return users;
}

/**
 * Example 3: Organizations with filtering
 */
export async function filterOrganizations() {
	console.log("🔹 Simple Layer: Filter organizations");
	
	const orgs = await paginate.organizations({
		page: 1,
		limit: 15,
		sort: "name:asc",
		filter: {
			type: "TEAM",
		},
	});
	
	console.log(`Found ${orgs.data.length} team organizations`);
	return orgs;
}

// ===== STRUCTURED LAYER EXAMPLES =====

/**
 * Example 4: Advanced user search with complex configuration
 */
export async function advancedUserSearch() {
	console.log("🔸 Structured Layer: Advanced user search");
	
	const users = await paginate.structured.users({
		page: 1,
		limit: 10,
		search: {
			term: "developer",
			fields: ["name", "email"],
			mode: "insensitive",
		},
		sort: [
			{ field: "name", order: "asc" },
			{ field: "createdAt", order: "desc" },
		],
		filter: {
			emailVerified: true,
		},
		select: {
			id: true,
			name: true,
			email: true,
			createdAt: true,
		},
	});
	
	console.log(`Found ${users.data.length} verified developers`);
	return users;
}

/**
 * Example 5: Organizations with member data
 */
export async function organizationsWithMembers() {
	console.log("🔸 Structured Layer: Organizations with member data");
	
	const orgs = await paginate.structured.organizations({
		page: 1,
		limit: 10,
		search: "tech",
		sort: "createdAt:desc",
		include: {
			members: {
				select: {
					id: true,
					role: true,
					user: {
						select: {
							name: true,
							email: true,
						},
					},
				},
			},
			_count: {
				select: {
					members: true,
				},
			},
		},
	});
	
	console.log(`Found ${orgs.data.length} tech organizations with member data`);
	return orgs;
}

// ===== CURSOR PAGINATION EXAMPLES =====

/**
 * Example 6: Infinite scroll with cursor pagination
 */
export async function infiniteScrollUsers() {
	console.log("🔄 Cursor Layer: Infinite scroll users");
	
	// First page
	const firstPage = await paginate.cursor.users({
		limit: 20,
		sort: "createdAt:desc",
		filter: {
			emailVerified: true,
		},
	});
	
	console.log(`First page: ${firstPage.data.length} users`);
	console.log(`Has more: ${firstPage.meta.hasNext}`);
	console.log(`Next cursor: ${firstPage.meta.nextCursor}`);
	
	// Next page using cursor
	if (firstPage.meta.nextCursor) {
		const nextPage = await paginate.cursor.users({
			cursor: firstPage.meta.nextCursor,
			limit: 20,
			sort: "createdAt:desc",
			filter: {
				emailVerified: true,
			},
		});
		
		console.log(`Next page: ${nextPage.data.length} users`);
		return { firstPage, nextPage };
	}
	
	return { firstPage };
}

// ===== ADVANCED LAYER EXAMPLES =====

/**
 * Example 7: Advanced pagination with full Prisma control
 */
export async function advancedPagination() {
	console.log("🔺 Advanced Layer: Full Prisma control");
	
	const result = await paginate.advanced("user", {
		pagination: {
			type: "page",
			page: 1,
			limit: 15,
		},
		where: {
			AND: [
				{
					emailVerified: true,
				},
				{
					OR: [
						{
							name: {
								contains: "admin",
								mode: "insensitive",
							},
						},
						{
							email: {
								endsWith: "@company.com",
							},
						},
					],
				},
			],
		},
		orderBy: [
			{ name: "asc" },
			{ createdAt: "desc" },
		],
		include: {
			organizationMemberships: {
				include: {
					organization: {
						select: {
							name: true,
							type: true,
						},
					},
				},
			},
		},
	});
	
	console.log(`Advanced query returned ${result.data.length} results`);
	return result;
}

/**
 * Example 8: Advanced cursor pagination
 */
export async function advancedCursorPagination() {
	console.log("🔺 Advanced Layer: Advanced cursor pagination");
	
	const result = await paginate.advanced("organizationMember", {
		pagination: {
			type: "cursor",
			limit: 25,
		},
		where: {
			role: {
				in: ["ADMIN", "COLLABORATOR"],
			},
			user: {
				emailVerified: true,
			},
		},
		orderBy: {
			joinedAt: "desc",
		},
		include: {
			user: {
				select: {
					id: true,
					name: true,
					email: true,
				},
			},
			organization: {
				select: {
					id: true,
					name: true,
					type: true,
				},
			},
		},
	});
	
	console.log(`Advanced cursor query returned ${result.data.length} results`);
	return result;
}

/**
 * Example 9: Using the core generic functions directly
 */
export async function directGenericUsage() {
	console.log("⚙️ Direct Generic Usage: Using core functions");
	
	// Use the core generic function directly for any model
	const invitations = await paginate.model("invitation", {
		page: 1,
		limit: 10,
		where: {
			status: "PENDING",
		},
		orderBy: {
			createdAt: "desc",
		},
		include: {
			organization: {
				select: {
					name: true,
				},
			},
			invitedBy: {
				select: {
					name: true,
				},
			},
		},
	});
	
	console.log(`Found ${invitations.data.length} pending invitations`);
	return invitations;
}

// ===== TEST RUNNER =====

/**
 * Run all pagination examples
 */
export async function runAllPaginationExamples() {
	console.log("🚀 Testing Generic Pagination System");
	console.log("====================================");
	
	try {
		// Simple Layer
		await basicUserPagination();
		await searchUsersWithSort();
		await filterOrganizations();
		
		// Structured Layer
		await advancedUserSearch();
		await organizationsWithMembers();
		
		// Cursor Layer
		await infiniteScrollUsers();
		
		// Advanced Layer
		await advancedPagination();
		await advancedCursorPagination();
		
		// Direct Generic Usage
		await directGenericUsage();
		
		console.log("\n✅ All pagination examples completed successfully!");
		console.log("====================================");
		console.log("✅ Generic types working correctly");
		console.log("✅ No 'any' types used");
		console.log("✅ Full type safety maintained");
		console.log("✅ Works with all Prisma models");
		
	} catch (error) {
		console.error("\n❌ Pagination examples failed:", error);
		throw error;
	}
}

// Run examples if this file is executed directly
if (require.main === module) {
	runAllPaginationExamples()
		.then(() => {
			console.log("\n✅ Examples completed successfully!");
			process.exit(0);
		})
		.catch((error) => {
			console.error("\n❌ Examples failed:", error);
			process.exit(1);
		});
}
