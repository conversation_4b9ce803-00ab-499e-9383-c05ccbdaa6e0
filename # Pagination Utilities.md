# Pagination Utilities

This guide covers the pagination utilities available in the SaaS Starter project, which provide both cursor-based and page-based pagination approaches for efficient data handling.

## Overview

The pagination system offers two distinct approaches:

- **Cursor Pagination**: Ideal for real-time feeds, infinite scroll, and large datasets where consistent ordering is crucial
- **Page Pagination**: Perfect for traditional page navigation with numbered pages and jump-to-page functionality

Both approaches are built on top of Prisma ORM and provide type-safe, reusable pagination logic.

## Cursor Pagination

### When to Use Cursor Pagination

Use cursor pagination when you need:
- Infinite scroll functionality
- Real-time feeds where new items are frequently added
- Large datasets where performance is critical
- Consistent pagination results even when data changes

### Function Signature

```typescript
async function cursorPagination<T extends PrismaModels>(
  prisma: PrismaClient,
  model: T,
  params: PaginationParams<T>
)
```

### Parameters

```typescript
type PaginationParams<T extends PrismaModels> = {
  cursor?: string;           // ID of the last item from previous page
  take?: number;            // Number of items to fetch (default: 10)
  skip?: number;            // Number of items to skip (default: 0)
  orderBy?: Prisma.Args<PrismaClient[T], 'findMany'>['orderBy'];
  where?: Prisma.Args<PrismaClient[T], 'findMany'>['where'];
  include?: Prisma.Args<PrismaClient[T], 'findMany'>['include'];
};
```

### Return Value

```typescript
{
  items: T[];              // Array of fetched items
  nextCursor: string | null; // ID for next page (null if no more items)
  totalCount: number;      // Total number of items matching the query
  hasMore: boolean;        // Whether there are more items to fetch
}
```

### Usage Example

```typescript
// In a server action
import { cursorPagination } from '@/lib/pagination';
import { db } from '@/lib/db';

export async function getPosts(params = {}) {
  const {
    cursor,
    take = 10,
    orderBy = { createdAt: 'desc' },
    filter = {}
  } = params;

  const paginationParams = {
    cursor,
    take,
    orderBy,
    where: {
      published: true,
      // Add your filters here
    },
    include: {
      author: {
        select: {
          id: true,
          name: true,
          image: true,
        }
      },
      tags: true,
    }
  };

  const result = await cursorPagination(db, 'post', paginationParams);
  
  return {
    items: result.items,
    nextCursor: result.nextCursor,
    totalCount: result.totalCount,
    hasMore: result.hasMore,
  };
}
```

### React Component Usage

```typescript
// Using with React Query for infinite scroll
import { useInfiniteQuery } from '@tanstack/react-query';

function PostList() {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery({
    queryKey: ['posts'],
    queryFn: async ({ pageParam }) => {
      return await getPosts({
        cursor: pageParam as string | undefined,
        take: 10,
      });
    },
    initialPageParam: null as string | null,
    getNextPageParam: (lastPage) =>
      lastPage.hasMore ? lastPage.nextCursor : undefined,
  });

  return (
    <div>
      {data?.pages.map((page, i) => (
        <div key={i}>
          {page.items.map((post) => (
            <div key={post.id}>{post.title}</div>
          ))}
        </div>
      ))}
      
      {hasNextPage && (
        <button 
          onClick={() => fetchNextPage()}
          disabled={isFetchingNextPage}
        >
          {isFetchingNextPage ? 'Loading...' : 'Load More'}
        </button>
      )}
    </div>
  );
}
```

## Page Pagination

### When to Use Page Pagination

Use page pagination when you need:
- Traditional numbered page navigation
- Jump-to-page functionality
- Display of total pages and current page
- Admin interfaces and data tables

### Function Signature

```typescript
async function pagePagination<T extends PrismaModels>(
  prisma: PrismaClient,
  model: T,
  params: PagePaginationParams<T>
)
```

### Parameters

```typescript
type PagePaginationParams<T extends PrismaModels> = Omit<
  PaginationParams<T>,
  'cursor'
> & {
  page?: number;            // Page number (1-based, default: 1)
  take?: number;            // Items per page (default: ITEMS_PER_PAGE = 6)
  orderBy?: Prisma.Args<PrismaClient[T], 'findMany'>['orderBy'];
  where?: Prisma.Args<PrismaClient[T], 'findMany'>['where'];
  include?: Prisma.Args<PrismaClient[T], 'findMany'>['include'];
};
```

### Return Value

```typescript
{
  items: T[];              // Array of fetched items
  page: number;            // Current page number
  totalPages: number;      // Total number of pages
  totalCount: number;      // Total number of items
  orderBy: string | null;  // Field used for ordering
  order: Order;            // Sort direction ('asc' | 'desc')
}
```

### Usage Example

```typescript
// In a server action
import { pagePagination } from '@/lib/pagination';
import { ITEMS_PER_PAGE } from '@/constants';
import { db } from '@/lib/db';

export async function getUsers(params = {}) {
  const {
    page = 1,
    take = ITEMS_PER_PAGE,
    orderBy = { createdAt: 'desc' },
    filter = {}
  } = params;

  const { searchQuery } = filter;

  const paginationParams = {
    page,
    take,
    orderBy,
    where: {
      ...(searchQuery && {
        OR: [
          { name: { contains: searchQuery, mode: 'insensitive' } },
          { email: { contains: searchQuery, mode: 'insensitive' } },
        ]
      })
    }
  };

  const result = await pagePagination(db, 'user', paginationParams);
  
  return {
    items: result.items,
    page: result.page,
    totalPages: result.totalPages,
    totalCount: result.totalCount,
    orderBy: result.orderBy,
    order: result.order,
    filter: searchQuery || '',
  };
}
```

### React Component Usage

```typescript
// In a Next.js page component
export default async function UsersPage({ searchParams }) {
  const page = parseInt(searchParams.page) || 1;
  const searchQuery = searchParams.q || '';
  
  const users = await getUsers({
    page,
    take: 6,
    ...(searchQuery && { filter: { searchQuery } }),
  });

  return (
    <div>
      <UsersTable data={users} />
      <Pagination
        totalItems={users.totalCount}
        itemsPerPage={6}
        currentPage={users.page}
        onPageChange={(newPage) => {
          // Navigate to new page
          router.push(`/users?page=${newPage}&q=${searchQuery}`);
        }}
      />
    </div>
  );
}
```

## Helper Functions

### calculateNewPage

This utility function helps determine the appropriate page to navigate to after adding or deleting items.

```typescript
function calculateNewPage(
  currentPage: number,
  totalItems: number,
  itemsPerPage: number,
  isAdding: boolean = false
): number
```

#### Parameters

- `currentPage`: The current page number
- `totalItems`: Total number of items after the operation
- `itemsPerPage`: Number of items per page
- `isAdding`: Whether an item is being added (true) or deleted (false)

#### Usage Example

```typescript
// When adding a new item
const handleAddUser = async (userData) => {
  await createUser(userData);
  const newTotalItems = totalItems + 1;
  const newPage = calculateNewPage(
    currentPage,
    newTotalItems,
    itemsPerPage,
    true // isAdding = true
  );
  setPage(newPage);
};

// When deleting an item
const handleDeleteUser = async (userId) => {
  await deleteUser(userId);
  const newTotalItems = totalItems - 1;
  const newPage = calculateNewPage(
    currentPage,
    newTotalItems,
    itemsPerPage,
    false // isAdding = false
  );
  setPage(newPage);
};
```

## Best Practices

### Performance Considerations

1. **Use appropriate page sizes**: Default `ITEMS_PER_PAGE = 6` is optimized for the UI
2. **Add database indexes**: Ensure indexed columns for `orderBy` fields
3. **Limit included relations**: Only include necessary related data
4. **Use cursor pagination for large datasets**: Better performance than offset-based pagination

### When to Choose Each Approach

**Choose Cursor Pagination when:**
- Building infinite scroll interfaces
- Dealing with real-time data feeds
- Working with large datasets (>10,000 items)
- Data consistency during pagination is critical

**Choose Page Pagination when:**
- Building traditional admin interfaces
- Users need to jump to specific pages
- Displaying page numbers is important for UX
- Working with smaller, stable datasets

### Common Pitfalls to Avoid

1. **Don't mix pagination types**: Use either cursor or page pagination consistently
2. **Handle empty states**: Always check for empty results and display appropriate UI
3. **Validate page parameters**: Ensure page numbers are within valid ranges
4. **Consider timezone issues**: Use consistent timezone handling for date-based ordering
5. **Test edge cases**: Test with single items, empty results, and boundary conditions

## Type Safety

The pagination utilities are fully type-safe and work with any Prisma model:

```typescript
// Type-safe usage with different models
const posts = await cursorPagination(db, 'post', params);     // Returns Post[]
const users = await pagePagination(db, 'user', params);      // Returns User[]
const apiKeys = await pagePagination(db, 'apiKey', params);  // Returns ApiKey[]
```

The TypeScript compiler will ensure that your `where`, `orderBy`, and `include` parameters match the selected model's schema.
